package com.data.platform.datamind.server.datameta.controller.web;

import com.data.platform.datamind.framework.common.pojo.CommonResult;
import com.data.platform.datamind.framework.common.pojo.PageResult;
import com.data.platform.datamind.server.datameta.convert.MetaDatabaseConvert;
import com.data.platform.datamind.server.datameta.convert.MetaTableConvert;
import com.data.platform.datamind.server.datameta.convert.MetaColumnConvert;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.entity.ResponseResult;
import com.data.platform.datamind.server.datameta.entity.ao.SyncAO;
import com.data.platform.datamind.server.datameta.vo.SyncVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabasePageReqVO;
import com.data.platform.datamind.server.datameta.vo.database.MetaDatabaseRespVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTablePageReqVO;
import com.data.platform.datamind.server.datameta.vo.table.MetaTableRespVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnPageReqVO;
import com.data.platform.datamind.server.datameta.vo.column.MetaColumnRespVO;
import com.data.platform.datamind.server.datameta.service.admin.MetaDatabaseService;
import com.data.platform.datamind.server.datameta.service.admin.MetaTableService;
import com.data.platform.datamind.server.datameta.service.admin.MetaColumnService;
import com.data.platform.datamind.server.datameta.service.web.impl.SyncDBMetaInfoService;
import com.data.platform.datamind.server.datameta.service.web.MetaDataEnhancedService;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncReqVO;
import com.data.platform.datamind.server.datameta.vo.metadata.MetaDataSyncRespVO;
import com.data.platform.datamind.server.datameta.vo.lineage.TableLineageRespVO;
import com.data.platform.datamind.server.datameta.vo.quality.DataQualityReportRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "元数据管理 - Web端")
@RestController
@RequestMapping({"/meta/"})
public class MetaController {

    @Autowired
    private SyncDBMetaInfoService syncDBMetaInfoService;

    @Resource
    private MetaDatabaseService databaseService;

    @Resource
    private MetaTableService tableService;

    @Resource
    private MetaColumnService columnService;

    @Resource
    private MetaDataEnhancedService metaDataEnhancedService;

    @GetMapping({"syncAllDbMeta"})
    @Operation(summary = "同步所有数据库元数据")
    public ResponseResult<SyncVO> syncAllDbMeta(@RequestParam(required = false) String dbType) {
        return ResponseResult.success(this.syncDBMetaInfoService.syncAllDBMetaInfo(dbType));
    }

    @PostMapping({"syncInstance"})
    @Operation(summary = "同步指定实例元数据")
    public ResponseResult<Void> syncInstance(@RequestBody SyncAO syncAO) {
        this.syncDBMetaInfoService.syncInstance(syncAO);
        return ResponseResult.success();
    }

    // ==================== 数据库相关接口 ====================

    @GetMapping("/databases")
    @Operation(summary = "获取数据库列表")
    public CommonResult<List<MetaDatabaseRespVO>> getDatabaseList() {
        List<MetaDatabaseDO> list = databaseService.getAllDatabases();
        return CommonResult.success(MetaDatabaseConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/databases/page")
    @Operation(summary = "获取数据库分页列表")
    public CommonResult<PageResult<MetaDatabaseRespVO>> getDatabasePage(@Valid MetaDatabasePageReqVO pageReqVO) {
        PageResult<MetaDatabaseDO> pageResult = databaseService.getDatabasePage(pageReqVO);
        return CommonResult.success(MetaDatabaseConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/databases/{id}")
    @Operation(summary = "获取数据库详情")
    @Parameter(name = "id", description = "数据库ID", required = true)
    public CommonResult<MetaDatabaseRespVO> getDatabaseDetail(@PathVariable("id") Long id) {
        MetaDatabaseDO database = databaseService.getDatabase(id);
        return CommonResult.success(MetaDatabaseConvert.INSTANCE.convert(database));
    }

    // ==================== 表相关接口 ====================

    @GetMapping("/tables")
    @Operation(summary = "获取表列表")
    public CommonResult<PageResult<MetaTableRespVO>> getTablePage(@Valid MetaTablePageReqVO pageReqVO) {
        PageResult<MetaTableDO> pageResult = tableService.getTablePage(pageReqVO);
        return CommonResult.success(MetaTableConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/tables/{id}")
    @Operation(summary = "获取表详情")
    @Parameter(name = "id", description = "表ID", required = true)
    public CommonResult<MetaTableRespVO> getTableDetail(@PathVariable("id") Long id) {
        MetaTableDO table = tableService.getTable(id);
        return CommonResult.success(MetaTableConvert.INSTANCE.convert(table));
    }

    @GetMapping("/databases/{databaseId}/tables")
    @Operation(summary = "获取指定数据库的表列表")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public CommonResult<List<MetaTableRespVO>> getTablesByDatabase(@PathVariable("databaseId") Long databaseId) {
        List<MetaTableDO> tables = tableService.getTablesByDbId(databaseId);
        return CommonResult.success(MetaTableConvert.INSTANCE.convertList(tables));
    }

    // ==================== 列相关接口 ====================

    @GetMapping("/columns")
    @Operation(summary = "获取列分页列表")
    public CommonResult<PageResult<MetaColumnRespVO>> getColumnPage(@Valid MetaColumnPageReqVO pageReqVO) {
        PageResult<MetaColumnDO> pageResult = columnService.getColumnPage(pageReqVO);
        return CommonResult.success(MetaColumnConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/tables/{tableId}/columns")
    @Operation(summary = "获取指定表的列列表")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public CommonResult<List<MetaColumnRespVO>> getColumnsByTable(@PathVariable("tableId") Long tableId) {
        List<MetaColumnDO> columns = columnService.getColumnsByTableId(tableId);
        return CommonResult.success(MetaColumnConvert.INSTANCE.convertList(columns));
    }

    @GetMapping("/columns/{id}")
    @Operation(summary = "获取列详情")
    @Parameter(name = "id", description = "列ID", required = true)
    public CommonResult<MetaColumnRespVO> getColumnDetail(@PathVariable("id") Long id) {
        MetaColumnDO column = columnService.getColumn(id);
        return CommonResult.success(MetaColumnConvert.INSTANCE.convert(column));
    }

    // ==================== 搜索相关接口 ====================

    @GetMapping("/search/tables")
    @Operation(summary = "搜索表")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "databaseId", description = "数据库ID（可选）", required = false)
    public CommonResult<List<MetaTableRespVO>> searchTables(@RequestParam("keyword") String keyword,
                                                           @RequestParam(value = "databaseId", required = false) Long databaseId) {
        MetaTablePageReqVO pageReqVO = new MetaTablePageReqVO();
        pageReqVO.setTableName(keyword);
        if (databaseId != null) {
            pageReqVO.setDbId(databaseId);
        }
        pageReqVO.setPageSize(50); // 限制搜索结果数量

        PageResult<MetaTableDO> pageResult = tableService.getTablePage(pageReqVO);
        return CommonResult.success(MetaTableConvert.INSTANCE.convertList(pageResult.getList()));
    }

    @GetMapping("/search/columns")
    @Operation(summary = "搜索列")
    @Parameter(name = "keyword", description = "搜索关键词", required = true)
    @Parameter(name = "tableId", description = "表ID（可选）", required = false)
    public CommonResult<List<MetaColumnRespVO>> searchColumns(@RequestParam("keyword") String keyword,
                                                             @RequestParam(value = "tableId", required = false) Long tableId) {
        MetaColumnPageReqVO pageReqVO = new MetaColumnPageReqVO();
        pageReqVO.setColumnName(keyword);
        if (tableId != null) {
            pageReqVO.setTableId(tableId);
        }
        pageReqVO.setPageSize(50); // 限制搜索结果数量

        PageResult<MetaColumnDO> pageResult = columnService.getColumnPage(pageReqVO);
        return CommonResult.success(MetaColumnConvert.INSTANCE.convertList(pageResult.getList()));
    }

    // ==================== 统计相关接口 ====================

    @GetMapping("/stats/overview")
    @Operation(summary = "获取元数据概览统计")
    public CommonResult<Object> getMetadataOverview() {
        // 获取统计数据
        List<MetaDatabaseDO> databases = databaseService.getAllDatabases();
        List<MetaTableDO> tables = tableService.getAllTables();
        List<MetaColumnDO> columns = columnService.getAllColumns();

        // 构建统计结果
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("databaseCount", databases.size());
        stats.put("tableCount", tables.size());
        stats.put("columnCount", columns.size());

        // 按数据库类型统计
        java.util.Map<String, Long> dbTypeStats = databases.stream()
            .collect(java.util.stream.Collectors.groupingBy(
                MetaDatabaseDO::getType,
                java.util.stream.Collectors.counting()));
        stats.put("databaseTypeStats", dbTypeStats);

        return CommonResult.success(stats);
    }

    @GetMapping("/stats/database/{databaseId}")
    @Operation(summary = "获取指定数据库的统计信息")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public CommonResult<Object> getDatabaseStats(@PathVariable("databaseId") Long databaseId) {
        List<MetaTableDO> tables = tableService.getTablesByDbId(databaseId);
        List<MetaColumnDO> columns = columnService.getColumnsByDbId(databaseId);

        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("tableCount", tables.size());
        stats.put("columnCount", columns.size());

        // 计算平均每表列数
        if (!tables.isEmpty()) {
            stats.put("avgColumnsPerTable", (double) columns.size() / tables.size());
        } else {
            stats.put("avgColumnsPerTable", 0.0);
        }

        return CommonResult.success(stats);
    }

    // ==================== 任务9: 元数据管理API增强 ====================

    @PostMapping("/metadata/sync")
    @Operation(summary = "元数据自动同步")
    public CommonResult<MetaDataSyncRespVO> syncMetaData(@Valid @RequestBody MetaDataSyncReqVO reqVO) {
        MetaDataSyncRespVO result = metaDataEnhancedService.syncMetaData(reqVO);
        return CommonResult.success(result);
    }

    @GetMapping("/lineage/{tableId}")
    @Operation(summary = "查询表血缘关系")
    @Parameter(name = "tableId", description = "表ID", required = true)
    public CommonResult<TableLineageRespVO> getTableLineage(@PathVariable("tableId") Long tableId) {
        TableLineageRespVO result = metaDataEnhancedService.getTableLineage(tableId);
        return CommonResult.success(result);
    }

    @GetMapping("/quality/report")
    @Operation(summary = "获取数据质量报告")
    public CommonResult<DataQualityReportRespVO> getDataQualityReport() {
        DataQualityReportRespVO result = metaDataEnhancedService.getDataQualityReport();
        return CommonResult.success(result);
    }

    @GetMapping("/quality/report/{databaseId}")
    @Operation(summary = "获取指定数据库的数据质量报告")
    @Parameter(name = "databaseId", description = "数据库ID", required = true)
    public CommonResult<DataQualityReportRespVO> getDataQualityReport(@PathVariable("databaseId") Long databaseId) {
        DataQualityReportRespVO result = metaDataEnhancedService.getDataQualityReport(databaseId);
        return CommonResult.success(result);
    }

}