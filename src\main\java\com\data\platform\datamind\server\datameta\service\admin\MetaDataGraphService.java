package com.data.platform.datamind.server.datameta.service.admin;

import com.data.platform.datamind.server.datameta.dal.dataobject.MetaColumnDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaDatabaseDO;
import com.data.platform.datamind.server.datameta.dal.dataobject.MetaTableDO;

import java.util.List;

/**
 * 元数据知识图谱 Service 接口
 * 负责将关系型元数据同步到 Neo4j 图谱，并提供图谱查询能力
 *
 */
public interface MetaDataGraphService {

    /**
     * 全量同步所有元数据到 Neo4j 图谱
     *
     * @param databases 所有数据库配置DO列表
     * @param tables 所有表DO列表
     * @param columns 所有列DO列表
     */
    void syncAllMetaDataToGraph(List<MetaDatabaseDO> databases,
                                List<MetaTableDO> tables,
                                List<MetaColumnDO> columns);

    // TODO: 增加图谱查询方法，例如：
    // List<GraphNode> getTableGraph(Long tableId);
    // List<ColumnLineage> getColumnLineage(Long columnId);
}
